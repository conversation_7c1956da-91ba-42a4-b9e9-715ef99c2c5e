import { zod<PERSON><PERSON><PERSON>ver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { z, ZodIssueCode } from "zod";

import { <PERSON><PERSON>, CloseButton } from "@/components/ui/button";
import {
  Checkbox,
  Form,
  InputField,
  Select,
  Textarea,
} from "@/components/ui/form";
import { Label } from "@/components/ui/form/label";
import { LazySelect } from "@/components/ui/lazy-select";
import { Modal } from "@/components/ui/modal";
import { useInfiniteCategoryVersion } from "@/hooks/queries/use-infinite-category-versions";
import { useInfiniteISFRefModels } from "@/hooks/queries/use-infinite-isf-ref-models";
import { useInfiniteTMFRefModels } from "@/hooks/queries/use-infinite-tmf-ref-models";
import type { ArtifactCategory } from "@/lib/apis/artifact-categories";
import { capitalize } from "@/utils/string";

import {
  useAddArtifactCategory,
  useEditArtifactCategory,
} from "./hooks/use-artifact-category-mutations";

export const IIT_STUDY_ARTIFACTS = [
  "recommended",
  "dependent",
  "mandatory",
] as const;

export const TMF_CORE = ["recommended", "core"] as const;

export const ORIGINS = [
  { label: "TMF", value: "to_TMF" },
  { label: "ISF", value: "to_ISF" },
] as const;

const TMF_FIELDS = [
  "tmfZoneNumber",
  "tmfZoneName",
  "tmfSectionNumber",
  "tmfSectionName",
  "tmfRecordGroupNumber",
  "tmfRecordGroupName",
] as const;

const ISF_FIELDS = [
  "isfZoneNumber",
  "isfZoneName",
  "isfSectionNumber",
  "isfSectionName",
  "isfRecordGroupNumber",
  "isfRecordGroupName",
] as const;

export const baseSchema = z.object({
  tmfRefModelId: z
    .string({ required_error: "TMF ref model is required" })
    .min(1, "TMF ref model is required"),
  isfRefModelId: z
    .string({ required_error: "ISF ref model is required" })
    .min(1, "ISF ref model is required"),
  categoryVersionId: z
    .string({
      invalid_type_error: "Category version is required",
      required_error: "Category version is required",
    })
    .min(1, "Category version is required"),

  tmfZoneNumber: z.string().nullable().optional(),
  tmfZoneName: z.string().nullable().optional(),
  tmfSectionNumber: z.string().nullable().optional(),
  tmfSectionName: z.string().nullable().optional(),
  tmfRecordGroupNumber: z.string().nullable().optional(),
  tmfRecordGroupName: z.string().nullable().optional(),

  isfZoneNumber: z.string().nullable().optional(),
  isfZoneName: z.string().nullable().optional(),
  isfSectionNumber: z.string().nullable().optional(),
  isfSectionName: z.string().nullable().optional(),
  isfRecordGroupNumber: z.string().nullable().optional(),
  isfRecordGroupName: z.string().nullable().optional(),

  recordType: z
    .string({
      invalid_type_error: "Record type is required",
      required_error: "Record type is required",
    })
    .min(1, "Record type is required"),
  alternativeNames: z.string().nullable().optional(),

  description: z.string().nullable().optional(),
  requiresSignature: z.boolean().nullable().optional(),
  expires: z.boolean().nullable().optional(),
  inspectableRecord: z.boolean().nullable().optional(),
  includesPHI: z.boolean().nullable().optional(),
  origin: z.enum(["to_ISF", "to_TMF", ""]).nullable().optional(),
  isTMF: z.boolean().default(false),
  isISF: z.boolean().default(false),
  tmfCore: z
    .enum([...TMF_CORE, ""])
    .nullable()
    .optional(),
  iitStudyArtifacts: z.enum(IIT_STUDY_ARTIFACTS, {
    errorMap: () => ({
      message: "Investigator Initiated Study Artifacts is required",
    }),
  }),
  isActive: z.boolean().default(true),
});

const schema = z.preprocess((input, ctx) => {
  const isfFields = baseSchema
    .pick({
      isISF: true,
      isfRecordGroupName: true,
      isfRecordGroupNumber: true,
      isfSectionName: true,
      isfSectionNumber: true,
      isfZoneName: true,
      isfZoneNumber: true,
    })
    .safeParse(input);
  if (isfFields.success) {
    const data = isfFields.data;
    if (data.isISF) {
      if (!data.isfRecordGroupName) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "ISF Record Group Name is required",
          path: ["isfRecordGroupName"],
        });
      }
      if (!data.isfRecordGroupNumber) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "ISF Record Group Number is required",
          path: ["isfRecordGroupNumber"],
        });
      }
      if (!data.isfSectionName) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "ISF Section Name is required",
          path: ["isfSectionName"],
        });
      }
      if (!data.isfSectionNumber) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "ISF Section Number is required",
          path: ["isfSectionNumber"],
        });
      }
      if (!data.isfZoneName) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "ISF Zone Name is required",
          path: ["isfZoneName"],
        });
      }
      if (!data.isfZoneNumber) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "ISF Zone Number is required",
          path: ["isfZoneNumber"],
        });
      }
    }
  }

  const tmfFields = baseSchema
    .pick({
      isTMF: true,
      tmfRecordGroupName: true,
      tmfRecordGroupNumber: true,
      tmfSectionName: true,
      tmfSectionNumber: true,
      tmfZoneName: true,
      tmfZoneNumber: true,
    })
    .safeParse(input);

  if (tmfFields.success) {
    const data = tmfFields.data;
    if (data.isTMF) {
      if (!data.tmfRecordGroupName) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "TMF Record Group Name is required",
          path: ["tmfRecordGroupName"],
        });
      }
      if (!data.tmfRecordGroupNumber) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "TMF Record Group Number is required",
          path: ["tmfRecordGroupNumber"],
        });
      }
      if (!data.tmfSectionName) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "TMF Section Name is required",
          path: ["tmfSectionName"],
        });
      }
      if (!data.tmfSectionNumber) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "TMF Section Number is required",
          path: ["tmfSectionNumber"],
        });
      }
      if (!data.tmfZoneName) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "TMF Zone Name is required",
          path: ["tmfZoneName"],
        });
      }
      if (!data.tmfZoneNumber) {
        ctx.addIssue({
          code: ZodIssueCode.custom,
          message: "TMF Zone Number is required",
          path: ["tmfZoneNumber"],
        });
      }
    }
  }

  const belongsFields = baseSchema
    .pick({
      isTMF: true,
      isISF: true,
    })
    .safeParse(input);

  if (belongsFields.success) {
    const data = belongsFields.data;
    if (!data.isTMF && !data.isISF) {
      ctx.addIssue({
        code: ZodIssueCode.custom,
        message: "TMF or ISF is required",
        path: ["isTMF", "isISF"],
      });
    }
  }

  return input;
}, baseSchema);

type ArtifactCategoryFormValues = z.infer<typeof schema>;

type ModalArtifactCategoryProps = {
  isOpen: boolean;
  onClose: () => void;
  artifactCategory?: ArtifactCategory;
};

export const ModalArtifactCategory = ({
  isOpen,
  onClose,
  artifactCategory,
}: ModalArtifactCategoryProps) => {
  const isEditingMode = Boolean(artifactCategory);

  const initialValues = {
    tmfRefModelId: artifactCategory?.tmfRefModelId || "",
    isfRefModelId: artifactCategory?.isfRefModelId || "",
    tmfZoneNumber: artifactCategory?.tmfZoneNumber || "",
    tmfZoneName: artifactCategory?.tmfZoneName || "",
    tmfSectionNumber: artifactCategory?.tmfSectionNumber || "",
    tmfSectionName: artifactCategory?.tmfSectionName || "",
    tmfRecordGroupNumber: artifactCategory?.tmfRecordGroupNumber || "",
    tmfRecordGroupName: artifactCategory?.tmfRecordGroupName || "",
    isfZoneNumber: artifactCategory?.isfZoneNumber || "",
    isfZoneName: artifactCategory?.isfZoneName || "",
    isfSectionNumber: artifactCategory?.isfSectionNumber || "",
    isfSectionName: artifactCategory?.isfSectionName || "",
    isfRecordGroupNumber: artifactCategory?.isfRecordGroupNumber || "",
    isfRecordGroupName: artifactCategory?.isfRecordGroupName || "",
    recordType: artifactCategory?.recordType || "",
    alternativeNames: artifactCategory?.alternativeNames || "",
    description: artifactCategory?.description || "",
    requiresSignature: artifactCategory?.requiresSignature || false,
    expires: artifactCategory?.expires || false,
    inspectableRecord: artifactCategory?.inspectableRecord || false,
    includesPHI: artifactCategory?.includesPHI || false,
    tmfCore: artifactCategory?.tmfCore || "",
    isTMF: artifactCategory?.isTMF || false,
    isISF: artifactCategory?.isISF || false,
    iitStudyArtifacts: artifactCategory?.iitStudyArtifacts || "recommended",
    isActive: isEditingMode ? artifactCategory?.isActive : true,
    categoryVersionId: artifactCategory?.categoryVersion?.id || "",
    origin: artifactCategory?.origin ?? undefined,
  } as const;

  const formMethods = useForm<ArtifactCategoryFormValues>({
    mode: "onChange",
    resolver: zodResolver(schema),
    defaultValues: initialValues,
  });

  const { mutateAsync: addArtifactCategory, isPending: isAdding } =
    useAddArtifactCategory();
  const { mutateAsync: editArtifactCategory, isPending: isEditing } =
    useEditArtifactCategory();

  async function onSubmit(data: ArtifactCategoryFormValues) {
    const payload = {
      ...data,
      origin: data.origin || undefined,
      tmfCore: data.tmfCore || undefined,
      iitStudyArtifacts: data.iitStudyArtifacts || undefined,
    };

    if (isEditingMode && artifactCategory) {
      await editArtifactCategory({
        id: artifactCategory.id!,
        ...payload,
      });
    } else {
      await addArtifactCategory(payload);
    }
    onClose();
  }

  useEffect(() => {
    const triggerFieldValidation = (fields: readonly string[]) => {
      fields.forEach((field) =>
        formMethods.trigger(field as keyof ArtifactCategoryFormValues),
      );
    };

    const { unsubscribe } = formMethods.watch((_, { name }) => {
      if (name === "isTMF" || name === "isISF") {
        triggerFieldValidation(ISF_FIELDS);
        triggerFieldValidation(TMF_FIELDS);
      }
    });
    return () => unsubscribe();
  }, [formMethods]);

  return (
    <Modal show={isOpen} onClose={onClose} size="4xl">
      <Modal.Header>
        {isEditingMode ? "Edit Artifact Category" : "Add Artifact Category"}
      </Modal.Header>
      <Modal.Body>
        <Form
          mode="onChange"
          schema={baseSchema}
          onSubmit={onSubmit}
          formMethods={formMethods}
        >
          <div className="grid gap-4 sm:grid-cols-2">
            <div className="flex flex-col gap-4 sm:col-span-2 sm:flex-row">
              <div className="flex-1 space-y-2">
                <Label htmlFor="tmfRefModelId">TMF Ref Model</Label>
                <LazySelect
                  id="tmfRefModelId"
                  name="tmfRefModelId"
                  placeholder="Enter TMF ref model..."
                  useInfiniteQuery={useInfiniteTMFRefModels}
                  getOptionLabel={(option) => option.tmfRefModel}
                  getOptionValue={(option) => option.id}
                />
              </div>
              <div className="flex-1 space-y-2">
                <Label htmlFor="isfRefModelId">ISF Ref Model</Label>
                <LazySelect
                  id="isfRefModelId"
                  name="isfRefModelId"
                  placeholder="Enter ISF ref model..."
                  useInfiniteQuery={useInfiniteISFRefModels}
                  getOptionLabel={(option) => option.isfRefModel}
                  getOptionValue={(option) => option.id}
                />
              </div>
              <div className="flex-1 space-y-2">
                <Label htmlFor="categoryVersionId">Category Version</Label>
                <LazySelect
                  id="categoryVersionId"
                  name="categoryVersionId"
                  placeholder="Select a version..."
                  useInfiniteQuery={useInfiniteCategoryVersion}
                  getOptionLabel={(option) => option.version.toString()}
                  getOptionValue={(option) => option.id}
                />
              </div>
            </div>

            <div className="space-y-4 sm:col-span-2">
              <h3 className="text-lg font-medium dark:text-white">
                TMF Fields
              </h3>
              <div className="grid gap-4 sm:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="tmfZoneNumber">TMF Zone Number</Label>
                  <InputField
                    id="tmfZoneNumber"
                    name="tmfZoneNumber"
                    placeholder="Enter TMF zone number..."
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tmfZoneName">TMF Zone Name</Label>
                  <InputField
                    id="tmfZoneName"
                    name="tmfZoneName"
                    placeholder="Enter TMF zone name..."
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tmfSectionNumber">TMF Section Number</Label>
                  <InputField
                    id="tmfSectionNumber"
                    name="tmfSectionNumber"
                    placeholder="Enter TMF section number..."
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tmfSectionName">TMF Section Name</Label>
                  <InputField
                    id="tmfSectionName"
                    name="tmfSectionName"
                    placeholder="Enter TMF section name..."
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tmfRecordGroupNumber">
                    TMF Record Group Number
                  </Label>
                  <InputField
                    id="tmfRecordGroupNumber"
                    name="tmfRecordGroupNumber"
                    placeholder="Enter TMF record group number..."
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="tmfRecordGroupName">
                    TMF Record Group Name
                  </Label>
                  <InputField
                    id="tmfRecordGroupName"
                    name="tmfRecordGroupName"
                    placeholder="Enter TMF record group name..."
                  />
                </div>
              </div>
            </div>

            <div className="space-y-4 sm:col-span-2">
              <h3 className="text-lg font-medium dark:text-white">
                ISF Fields
              </h3>
              <div className="grid gap-4 sm:grid-cols-2">
                <div className="space-y-2">
                  <Label htmlFor="isfZoneNumber">ISF Zone Number</Label>
                  <InputField
                    id="isfZoneNumber"
                    name="isfZoneNumber"
                    placeholder="Enter ISF zone number..."
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="isfZoneName">ISF Zone Name</Label>
                  <InputField
                    id="isfZoneName"
                    name="isfZoneName"
                    placeholder="Enter ISF zone name..."
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="isfSectionNumber">ISF Section Number</Label>
                  <InputField
                    id="isfSectionNumber"
                    name="isfSectionNumber"
                    placeholder="Enter ISF section number..."
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="isfSectionName">ISF Section Name</Label>
                  <InputField
                    id="isfSectionName"
                    name="isfSectionName"
                    placeholder="Enter ISF section name..."
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="isfRecordGroupNumber">
                    ISF Record Group Number
                  </Label>
                  <InputField
                    id="isfRecordGroupNumber"
                    name="isfRecordGroupNumber"
                    placeholder="Enter ISF record group number..."
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="isfRecordGroupName">
                    ISF Record Group Name
                  </Label>
                  <InputField
                    id="isfRecordGroupName"
                    name="isfRecordGroupName"
                    placeholder="Enter ISF record group name..."
                  />
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="recordType">Record Type</Label>
              <InputField
                id="recordType"
                name="recordType"
                placeholder="Enter record type..."
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="alternativeNames">Alternative Names</Label>
              <InputField
                id="alternativeNames"
                name="alternativeNames"
                placeholder="Enter alternative names..."
              />
            </div>

            <div className="space-y-2 sm:col-span-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                name="description"
                placeholder="Enter description..."
              />
            </div>

            <div className="flex flex-col space-y-2">
              <Label required>Belongs</Label>
              <div className="flex flex-1 gap-4">
                <div className="flex flex-1 items-center gap-2">
                  <Label htmlFor="isTMF" className="uppercase">
                    TMF
                  </Label>
                  <Checkbox id="isTMF" name="isTMF" />
                </div>
                <div className="flex flex-1 items-center gap-2">
                  <Label htmlFor="isISF" className="uppercase">
                    ISF
                  </Label>
                  <Checkbox id="isISF" name="isISF" />
                </div>
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="origin">Origin</Label>
              <Select id="origin" name="origin" options={ORIGINS} />
            </div>
            <div className="space-y-2">
              <Label htmlFor="tmfCore">TMF Core</Label>
              <Select
                id="tmfCore"
                name="tmfCore"
                options={TMF_CORE.map((opt) => ({
                  label: capitalize(opt),
                  value: opt,
                }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="iitStudyArtifacts">
                Investigator Initiated Study Artifacts
              </Label>
              <Select
                id="iitStudyArtifacts"
                name="iitStudyArtifacts"
                options={IIT_STUDY_ARTIFACTS.map((opt) => ({
                  label: capitalize(opt),
                  value: opt,
                }))}
              />
            </div>

            <div className="flex items-center gap-2">
              <Checkbox id="isActive" name="isActive" />
              <Label htmlFor="isActive">Active</Label>
            </div>

            <div className="space-y-4 sm:col-span-2">
              <h3 className="text-lg font-medium dark:text-white">
                Additional Properties
              </h3>
              <div className="grid gap-4 sm:grid-cols-2">
                <div className="flex items-center gap-2">
                  <Checkbox id="requiresSignature" name="requiresSignature" />
                  <Label htmlFor="requiresSignature">Requires Signature</Label>
                </div>

                <div className="flex items-center gap-2">
                  <Checkbox id="expires" name="expires" />
                  <Label htmlFor="expires">Expires</Label>
                </div>

                <div className="flex items-center gap-2">
                  <Checkbox id="inspectableRecord" name="inspectableRecord" />
                  <Label htmlFor="inspectableRecord">Inspectable Record</Label>
                </div>

                <div className="flex items-center gap-2">
                  <Checkbox id="includesPHI" name="includesPHI" />
                  <Label htmlFor="includesPHI">Includes PHI</Label>
                </div>
              </div>
            </div>
          </div>

          <div className="flex flex-col justify-end gap-5 border-none pt-4 sm:flex-row">
            <CloseButton onClose={onClose} />
            <Button
              type="submit"
              color="blue"
              isLoading={isAdding || isEditing}
            >
              Save
            </Button>
          </div>
        </Form>
      </Modal.Body>
    </Modal>
  );
};
